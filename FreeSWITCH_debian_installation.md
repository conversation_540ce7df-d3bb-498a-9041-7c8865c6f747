Debian
About
FreeSWITCH project on GitHub: https://github.com/signalwire/freeswitch
Commit Log: see all FreeSWITCH commits here: https://github.com/signalwire/freeswitch/commits/master
Please open new issues in: https://github.com/signalwire/freeswitch/issues

Authentication required
SignalWire Personal Access Tokens (PAT)s are required to access FreeSWITCH install packages.
HOWTO Create a SignalWire Personal Access Token

Note for different architectures
This instruction is for all supported architectures.
FSGET script detects your system's architecture automatically when preparing it for installing or building FreeSWITCH.
We do not provide packages for 32 bit x86 systems.

Sofia-sip and SpanDSP Notice!
SpanDSP and sofia-sip

Sofia-sip and SpanDSP dependencies have been removed from the FreeSWITCH™ tree since v1.10.4 Release (05 August 2020).

You can find source files of Sofia-sip here: https://github.com/freeswitch/sofia-sip

SpanDSP sources are here: https://github.com/freeswitch/spandsp

Packages for Sofia-sip and SpandDSP are available for all supported platforms from our packaging repos.

You might have to visit each directory, run ./bootstrap.sh then ./configure then make install

We dropped support in packaging for anything older than Debian 9 and anything older than Centos 7 due to a number of dependency issues on older platforms.

Installing FreeSWITCH - Package or Source
Installing From Debian Packages
Latest Release Package:
TOKEN=YOURSIGNALWIRETOKEN

# you may want to populate /etc/freeswitch at this point.
# if /etc/freeswitch does not exist, the standard vanilla configuration is deployed

apt update && apt install -y curl
curl -sSL https://freeswitch.org/fsget | bash -s $TOKEN release install

FreeSWITCH™ is now installed and can be accessed with

FreeSWITCH CLI
fs_cli -rRS

Master Branch ("git"):

WARNING not suitable for production

Master Test Package:
TOKEN=YOURSIGNALWIRETOKEN

# you may want to populate /etc/freeswitch at this point.
# if /etc/freeswitch does not exist, the standard vanilla configuration is deployed

apt update && apt install -y curl
curl -sSL https://freeswitch.org/fsget | bash -s $TOKEN prerelease install

Building Debian Packages From Master Branch
WARNING not suitable for production

The master branch depends on different libraries which are not available as packages in Debian distribution, but are available from FreeSWITCH repository. Thus, you will need:

internet access to the FreeSWITCH DEB repository using FSGET script or
follow the "How to build FreeSWITCH Dependencies" instruction.
Building packages is automated using FSDEB script.

Build your own .deb Master package
TOKEN=YOURSIGNALWIRETOKEN

apt update && apt install -y git curl
curl -sSL https://freeswitch.org/fsget | bash -s $TOKEN

cd /usr/src
git clone https://github.com/signalwire/freeswitch -b master

curl -sSL https://freeswitch.org/fsdeb | bash -s -- -b 999 -o /usr/src/fsdebs/ -w /usr/src/freeswitch
# If the build is successful, you will have a bunch of .deb files in /usr/src/fsdebs

Building From Source
Compiling Release Branch (production)
TOKEN=YOURSIGNALWIRETOKEN

apt update && apt install -y curl
curl -sSL https://freeswitch.org/fsget | bash -s $TOKEN
 
# Install dependencies required for the build
apt-get build-dep freeswitch
 
# then let's get the source. Use the -b flag to get a specific branch
cd /usr/src/
git clone -b v1.10 https://github.com/signalwire/freeswitch.git
cd freeswitch
 
# Because we're in a branch that will go through many rebases, it's
# better to set this one, or you'll get CONFLICTS when pulling (update).
git config pull.rebase true
 
# ... and do the build
./bootstrap.sh -j
./configure
make
make install

Compiling Latest Master (for testing)
WARNING not suitable for production

TOKEN=YOURSIGNALWIRETOKEN

apt update && apt install -y curl
curl -sSL https://freeswitch.org/fsget | bash -s $TOKEN
 
# Install dependencies required for the build
apt-get build-dep freeswitch

# Then let's get the source. Use the -b flag to get a specific branch
cd /usr/src/
git clone https://github.com/signalwire/freeswitch.git
cd freeswitch

# Because we're in a branch that will go through many rebases, it's
# better to set this one, or you'll get CONFLICTS when pulling (update).
git config pull.rebase true

# ... and do the build

# The -j argument spawns multiple threads to speed the build process, but causes trouble on some systems
./bootstrap.sh -j

# if you want to add or remove modules from the build, edit modules.conf
vi modules.conf

# add a module by removing '#' comment character at the beginning of the line
# remove a module by adding the '#' comment character at the beginning of the line 
# containing the name of the module to be skipped in the build process

./configure
make
make install

# Install audio files:
make cd-sounds-install cd-moh-install

# To update an installed build:
cd /usr/src/freeswitch
make current

Before Starting FreeSWITCH
If you built FreeSWITCH™ from source code you should set file permissions and ownership before starting FreeSWITCH. Follow the post-installation instructions.