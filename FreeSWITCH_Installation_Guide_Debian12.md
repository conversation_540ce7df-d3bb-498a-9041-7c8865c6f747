# FreeSWITCH Community Edition Installation Guide for Debian 12 (Bookworm)

## Table of Contents
1. [Pre-installation Requirements](#pre-installation-requirements)
2. [Installation Process](#installation-process)
3. [Post-installation Verification](#post-installation-verification)
4. [Troubleshooting](#troubleshooting)
5. [Service Management](#service-management)
6. [Configuration Reference](#configuration-reference)

---

## Pre-installation Requirements

### System Compatibility
- **Operating System**: Debian 12 (bookworm) 64-bit
- **Architecture**: amd64
- **Minimum RAM**: 1GB (2GB+ recommended)
- **Disk Space**: 2GB free space minimum
- **Network**: Internet connectivity for package downloads

### Required Dependencies
FreeSWITCH installation will automatically handle most dependencies, but ensure these are available:
```bash
# Update package lists
sudo apt update

# Install basic requirements
sudo apt install -y curl gnupg2 software-properties-common apt-transport-https
```

### Network Port Requirements
FreeSWITCH uses the following default ports:
- **5060** (TCP/UDP) - SIP signaling
- **5066** (TCP) - SIP TLS
- **5080** (TCP) - SIP WebSocket
- **7443** (TCP) - HTTPS
- **8021** (TCP) - Event Socket Layer (ESL)
- **8081** (TCP) - HTTP
- **8082** (TCP) - HTTPS
- **16384-32768** (UDP) - RTP media (configurable)

### System Verification Commands
```bash
# Check OS version
cat /etc/os-release

# Verify architecture
uname -m

# Check available disk space
df -h

# Check memory
free -h

# Verify network connectivity
ping -c 3 freeswitch.signalwire.com
```

---

## Installation Process

### Step 1: SignalWire Token Setup
FreeSWITCH Community Edition requires a SignalWire token for repository access.

1. **Obtain Token**: Visit [SignalWire](https://signalwire.com) and create a free account
2. **Set Token Variable**:
```bash
TOKEN=your_signalwire_token_here
echo "=== Using SignalWire Token for FreeSWITCH Installation ==="
```

### Step 2: Official Installation Script
Use the official SignalWire installation script:

```bash
# Download and execute installation script
sudo curl -sSL https://freeswitch.org/fsget | sudo bash -s $TOKEN release install
```

**Expected Output Indicators**:
- Repository addition confirmation
- Package list updates
- Dependency resolution
- Package downloads (366 MB approximately)
- Installation of 295+ packages
- Sound file generation for multiple languages

### Step 3: Permission Configuration
The installation may fail initially due to permission issues. Fix with:

```bash
# Create required directories
sudo mkdir -p /var/lib/freeswitch /var/log/freeswitch /var/run/freeswitch

# Set proper ownership
sudo chown -R freeswitch:freeswitch /var/lib/freeswitch /var/log/freeswitch /etc/freeswitch /usr/share/freeswitch /var/run/freeswitch
```

### Step 4: Service Startup
```bash
# Start FreeSWITCH service
sudo systemctl start freeswitch

# Enable auto-start on boot
sudo systemctl enable freeswitch

# Verify service status
sudo systemctl status freeswitch
```

**Expected Service Status**:
```
● freeswitch.service - freeswitch
   Loaded: loaded (/lib/systemd/system/freeswitch.service; enabled; preset: enabled)
   Active: active (running) since [timestamp]
   Main PID: [pid] (freeswitch)
   Tasks: 42 (limit: 1137)
   Memory: 67.3M
```

---

## Post-installation Verification

### Service Status Verification
```bash
# Check if service is active
sudo systemctl is-active freeswitch
# Expected output: active

# Check if service is enabled for auto-start
sudo systemctl is-enabled freeswitch
# Expected output: enabled

# Detailed service status
sudo systemctl status freeswitch
```

### CLI Functionality Testing
```bash
# Test FreeSWITCH CLI access
sudo fs_cli -x "status"
```

**Expected CLI Output**:
```
UP 0 years, 0 days, 0 hours, X minutes, X seconds
FreeSWITCH (Version 1.10.12) is ready
0 session(s) since startup
1000 session(s) max
min idle cpu 0.00/99.23
Current Stack Size/Max 240K/8192K
```

### Version Verification
```bash
# Check FreeSWITCH version
freeswitch -version
# Expected: FreeSWITCH version: 1.10.12-release-[build-info]
```

### Network Port Verification
```bash
# Install net-tools if not available
sudo apt install -y net-tools

# Check FreeSWITCH network bindings
sudo netstat -tlnp | grep freeswitch
```

**Expected Port Bindings**:
```
tcp    0  0  [IP]:5060    0.0.0.0:*    LISTEN    [PID]/freeswitch
tcp    0  0  [IP]:5066    0.0.0.0:*    LISTEN    [PID]/freeswitch
tcp    0  0  [IP]:5080    0.0.0.0:*    LISTEN    [PID]/freeswitch
tcp    0  0  [IP]:7443    0.0.0.0:*    LISTEN    [PID]/freeswitch
tcp    0  0  [IP]:8081    0.0.0.0:*    LISTEN    [PID]/freeswitch
tcp    0  0  [IP]:8082    0.0.0.0:*    LISTEN    [PID]/freeswitch
tcp6   0  0  :::8021      :::*         LISTEN    [PID]/freeswitch
```

### Module Loading Verification
```bash
# Check loaded modules count
sudo fs_cli -x "show modules" | wc -l
# Expected: 500+ modules

# List specific module categories
sudo fs_cli -x "show modules" | grep -E "(mod_sofia|mod_dptools|mod_conference)"
```

### Memory and Resource Checks
```bash
# Check FreeSWITCH process details
ps aux | grep freeswitch | grep -v grep

# Check memory usage
sudo fs_cli -x "status" | grep -E "(Memory|CPU)"
```

### Configuration File Verification
```bash
# Check configuration directory structure
ls -la /etc/freeswitch/

# Verify key configuration files exist
ls -la /etc/freeswitch/{freeswitch.xml,vars.xml}

# Check log files
sudo ls -la /var/log/freeswitch/
```

---

## Troubleshooting

### Common Installation Issues

#### Issue 1: Permission Denied Errors
**Symptoms**:
```
× freeswitch.service - freeswitch
   Active: failed (Result: exit-code)
   Process: ExecStartPre=/bin/chown [...] (code=exited, status=1/FAILURE)
```

**Solution**:
```bash
# Create missing directories
sudo mkdir -p /var/lib/freeswitch /var/log/freeswitch /var/run/freeswitch

# Fix ownership
sudo chown -R freeswitch:freeswitch /var/lib/freeswitch /var/log/freeswitch /etc/freeswitch /usr/share/freeswitch /var/run/freeswitch

# Restart service
sudo systemctl restart freeswitch
```

#### Issue 2: Repository Access Problems
**Symptoms**:
- "Could not resolve host" errors
- GPG key verification failures
- Package not found errors

**Solution**:
```bash
# Verify internet connectivity
ping -c 3 freeswitch.signalwire.com

# Check SignalWire token validity
echo $TOKEN

# Re-run installation with verbose output
sudo curl -sSL https://freeswitch.org/fsget | sudo bash -s $TOKEN release install
```

#### Issue 3: Service Won't Start
**Symptoms**:
- Service fails to start
- "Start request repeated too quickly" errors

**Solution**:
```bash
# Check service logs
sudo journalctl -u freeswitch.service -n 50

# Reset failed state
sudo systemctl reset-failed freeswitch

# Check configuration syntax
sudo freeswitch -t

# Start in foreground for debugging
sudo freeswitch -nf
```

### Log File Locations
```bash
# Main log file
sudo tail -f /var/log/freeswitch/freeswitch.log

# System service logs
sudo journalctl -u freeswitch.service -f

# Configuration validation log
sudo cat /var/log/freeswitch/freeswitch.xml.fsxml
```

### Diagnostic Commands
```bash
# Test configuration syntax
sudo freeswitch -t

# Check process status
pgrep -f freeswitch

# Verify user and group
id freeswitch

# Check file permissions
sudo find /etc/freeswitch -type f -not -user freeswitch
sudo find /var/log/freeswitch -type f -not -user freeswitch
```

---

## Service Management

### Basic Service Commands
```bash
# Start FreeSWITCH
sudo systemctl start freeswitch

# Stop FreeSWITCH
sudo systemctl stop freeswitch

# Restart FreeSWITCH
sudo systemctl restart freeswitch

# Reload configuration (graceful)
sudo systemctl reload freeswitch

# Check service status
sudo systemctl status freeswitch
```

### Auto-start Configuration
```bash
# Enable auto-start on boot
sudo systemctl enable freeswitch

# Disable auto-start
sudo systemctl disable freeswitch

# Check auto-start status
sudo systemctl is-enabled freeswitch
```

### CLI Management Commands
```bash
# Interactive CLI session
sudo fs_cli

# Execute single command
sudo fs_cli -x "command"

# Common CLI commands
sudo fs_cli -x "status"                    # System status
sudo fs_cli -x "show calls"                # Active calls
sudo fs_cli -x "show channels"             # Active channels
sudo fs_cli -x "reloadxml"                 # Reload configuration
sudo fs_cli -x "shutdown"                  # Graceful shutdown
sudo fs_cli -x "version"                   # Version information
sudo fs_cli -x "show modules"              # Loaded modules
```

---

## Configuration Reference

### Directory Structure
```
/etc/freeswitch/                    # Main configuration directory
├── freeswitch.xml                  # Main configuration file
├── vars.xml                        # Global variables
├── autoload_configs/               # Module configurations
├── dialplan/                       # Call routing logic
├── directory/                      # User directory
├── sip_profiles/                   # SIP profile configurations
└── tls/                           # TLS certificates

/var/log/freeswitch/               # Log files
├── freeswitch.log                 # Main log file
├── freeswitch.xml.fsxml           # Configuration parsing log
└── cdr-csv/                       # Call detail records

/var/lib/freeswitch/               # Runtime data
/usr/share/freeswitch/             # Sounds and resources
```

### Key Configuration Files
- **freeswitch.xml**: Main configuration file
- **vars.xml**: Global variables and settings
- **autoload_configs/modules.conf.xml**: Module loading configuration
- **sip_profiles/internal.xml**: Internal SIP profile
- **sip_profiles/external.xml**: External SIP profile
- **dialplan/default.xml**: Default dialplan

### Installation Summary
- **Version**: FreeSWITCH 1.10.12 Community Edition
- **Packages Installed**: 295+ packages
- **Disk Usage**: ~713 MB
- **Memory Usage**: ~67 MB (idle)
- **Modules**: 500+ available modules
- **Languages**: Multiple language packs included
- **Sound Files**: Multiple voice prompts in various languages

---

**Installation completed successfully!**
FreeSWITCH Community Edition is now ready for configuration and use.

For advanced configuration and usage, refer to the official FreeSWITCH documentation at https://freeswitch.org/confluence/

---

## Advanced Verification Procedures

### Comprehensive System Check Script
Create a verification script to automate post-installation checks:

```bash
#!/bin/bash
# FreeSWITCH Installation Verification Script

echo "=== FreeSWITCH Installation Verification ==="
echo

# Check service status
echo "1. Service Status:"
systemctl is-active freeswitch
echo

# Check process
echo "2. Process Check:"
pgrep -f freeswitch > /dev/null && echo "FreeSWITCH process running" || echo "FreeSWITCH process NOT running"
echo

# Check version
echo "3. Version Information:"
freeswitch -version 2>/dev/null || echo "Version check failed"
echo

# Check CLI connectivity
echo "4. CLI Test:"
timeout 10 fs_cli -x "status" 2>/dev/null | head -3 || echo "CLI test failed"
echo

# Check network ports
echo "5. Network Ports:"
netstat -tlnp 2>/dev/null | grep freeswitch | wc -l | xargs echo "FreeSWITCH listening on" "ports"
echo

# Check modules
echo "6. Module Count:"
timeout 10 fs_cli -x "show modules" 2>/dev/null | wc -l | xargs echo "Loaded modules:"
echo

# Check configuration files
echo "7. Configuration Files:"
[ -f /etc/freeswitch/freeswitch.xml ] && echo "Main config: OK" || echo "Main config: MISSING"
[ -f /etc/freeswitch/vars.xml ] && echo "Variables config: OK" || echo "Variables config: MISSING"
echo

# Check log files
echo "8. Log Files:"
[ -f /var/log/freeswitch/freeswitch.log ] && echo "Main log: OK" || echo "Main log: MISSING"
echo

# Check permissions
echo "9. Permission Check:"
[ "$(stat -c %U /etc/freeswitch)" = "freeswitch" ] && echo "Config permissions: OK" || echo "Config permissions: ISSUE"
[ "$(stat -c %U /var/log/freeswitch)" = "freeswitch" ] && echo "Log permissions: OK" || echo "Log permissions: ISSUE"
echo

echo "=== Verification Complete ==="
```

### Performance Monitoring
```bash
# Monitor FreeSWITCH performance
watch -n 5 'ps aux | grep freeswitch | grep -v grep'

# Check memory usage over time
while true; do
    echo "$(date): $(ps -o pid,ppid,cmd,%mem,%cpu | grep freeswitch | grep -v grep)"
    sleep 60
done

# Monitor active sessions
watch -n 2 'fs_cli -x "show calls count"'
```

### Security Considerations

#### Firewall Configuration
```bash
# Configure UFW firewall for FreeSWITCH
sudo ufw allow 5060/tcp comment "FreeSWITCH SIP"
sudo ufw allow 5060/udp comment "FreeSWITCH SIP"
sudo ufw allow 5066/tcp comment "FreeSWITCH SIP TLS"
sudo ufw allow 5080/tcp comment "FreeSWITCH SIP WS"
sudo ufw allow 8021/tcp comment "FreeSWITCH ESL"
sudo ufw allow 16384:32768/udp comment "FreeSWITCH RTP"

# Enable firewall
sudo ufw --force enable

# Check firewall status
sudo ufw status numbered
```

#### File Permission Audit
```bash
# Audit FreeSWITCH file permissions
find /etc/freeswitch -type f -not -user freeswitch -ls
find /var/log/freeswitch -type f -not -user freeswitch -ls
find /var/lib/freeswitch -type f -not -user freeswitch -ls

# Fix any permission issues found
sudo chown -R freeswitch:freeswitch /etc/freeswitch /var/log/freeswitch /var/lib/freeswitch
```

---

## Package Information

### Installed Package Details
The FreeSWITCH installation includes numerous packages:

#### Core Packages
- `freeswitch` - Main FreeSWITCH package
- `freeswitch-systemd` - Systemd service files
- `libfreeswitch1` - Core libraries
- `signalwire-client-c2` - SignalWire client libraries

#### Configuration Packages
- `freeswitch-conf-vanilla` - Default configuration
- `freeswitch-conf-curl` - cURL configuration
- `freeswitch-conf-sbc` - Session Border Controller configuration
- `freeswitch-conf-softphone` - Softphone configuration
- `freeswitch-conf-insideout` - Inside-out configuration

#### Language Support
- `freeswitch-lang-en` - English language pack
- `freeswitch-lang-es` - Spanish language pack
- `freeswitch-lang-fr` - French language pack
- `freeswitch-lang-de` - German language pack
- `freeswitch-lang-pt` - Portuguese language pack
- `freeswitch-lang-ru` - Russian language pack
- `freeswitch-lang-he` - Hebrew language pack

#### Sound Packages
- `freeswitch-sounds-en-us-callie` - English (US) sounds
- `freeswitch-sounds-es-ar-mario` - Spanish (Argentina) sounds
- `freeswitch-sounds-fr-ca-june` - French (Canada) sounds
- `freeswitch-sounds-pt-br-karina` - Portuguese (Brazil) sounds
- `freeswitch-sounds-ru-ru-vika` - Russian sounds
- `freeswitch-music-default` - Default music on hold

#### Module Packages (Selected)
- `freeswitch-mod-sofia` - SIP stack
- `freeswitch-mod-dptools` - Dialplan tools
- `freeswitch-mod-conference` - Conference bridge
- `freeswitch-mod-voicemail` - Voicemail system
- `freeswitch-mod-lua` - Lua scripting
- `freeswitch-mod-python3` - Python 3 scripting
- `freeswitch-mod-curl` - HTTP/cURL support
- `freeswitch-mod-xml-curl` - XML cURL support
- `freeswitch-mod-event-socket` - Event Socket Layer
- `freeswitch-mod-logfile` - File logging

### Package Verification
```bash
# List all FreeSWITCH packages
dpkg -l | grep freeswitch

# Check package integrity
sudo dpkg --verify freeswitch

# Get package information
apt show freeswitch

# Check package dependencies
apt depends freeswitch
```

---

## Backup and Recovery

### Configuration Backup
```bash
# Create configuration backup
sudo tar -czf freeswitch-config-backup-$(date +%Y%m%d).tar.gz /etc/freeswitch/

# Backup with verification
sudo tar -czf freeswitch-config-backup-$(date +%Y%m%d).tar.gz /etc/freeswitch/ && \
tar -tzf freeswitch-config-backup-$(date +%Y%m%d).tar.gz | head -10

# Restore configuration
sudo systemctl stop freeswitch
sudo tar -xzf freeswitch-config-backup-YYYYMMDD.tar.gz -C /
sudo chown -R freeswitch:freeswitch /etc/freeswitch/
sudo systemctl start freeswitch
```

### Database Backup (if using)
```bash
# Backup SQLite databases
sudo cp /var/lib/freeswitch/db/* /backup/location/

# Backup PostgreSQL (if configured)
pg_dump freeswitch > freeswitch-db-backup-$(date +%Y%m%d).sql
```

---

## Integration Testing

### SIP Client Testing
```bash
# Test SIP registration (using sipexer or similar tool)
# Install sipexer
wget https://github.com/miconda/sipexer/releases/download/v1.0.0/sipexer_1.0.0_linux_amd64.tar.gz
tar -xzf sipexer_1.0.0_linux_amd64.tar.gz

# Test SIP OPTIONS
./sipexer -M=options -fu=sip:test@your-server-ip:5060

# Test basic connectivity
telnet your-server-ip 5060
```

### Load Testing Preparation
```bash
# Install SIPp for load testing
sudo apt install -y sipp

# Basic SIP test scenario
sipp -sn uac your-server-ip:5060 -l 1 -m 1

# Monitor during testing
watch -n 1 'fs_cli -x "show calls count"'
```

---

## Maintenance Procedures

### Regular Maintenance Tasks
```bash
# Log rotation check
sudo logrotate -d /etc/logrotate.d/freeswitch

# Disk space monitoring
df -h /var/log/freeswitch/
du -sh /var/log/freeswitch/*

# Clean old CDR files (if needed)
find /var/log/freeswitch/cdr-csv/ -name "*.csv" -mtime +30 -delete

# Update FreeSWITCH (when updates available)
sudo apt update && sudo apt upgrade freeswitch*
```

### Health Check Script
```bash
#!/bin/bash
# FreeSWITCH Health Check

LOGFILE="/var/log/freeswitch-health.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$DATE] Starting FreeSWITCH health check" >> $LOGFILE

# Check if service is running
if ! systemctl is-active --quiet freeswitch; then
    echo "[$DATE] ERROR: FreeSWITCH service is not running" >> $LOGFILE
    sudo systemctl start freeswitch
fi

# Check CLI responsiveness
if ! timeout 10 fs_cli -x "status" >/dev/null 2>&1; then
    echo "[$DATE] WARNING: FreeSWITCH CLI not responding" >> $LOGFILE
fi

# Check disk space
DISK_USAGE=$(df /var/log/freeswitch | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "[$DATE] WARNING: Disk usage high: ${DISK_USAGE}%" >> $LOGFILE
fi

echo "[$DATE] Health check completed" >> $LOGFILE
```

---

## Support and Resources

### Official Documentation
- **FreeSWITCH Wiki**: https://freeswitch.org/confluence/
- **API Documentation**: https://freeswitch.org/confluence/display/FREESWITCH/FreeSWITCH+API
- **Dialplan Guide**: https://freeswitch.org/confluence/display/FREESWITCH/Dialplan

### Community Resources
- **Mailing Lists**: https://lists.freeswitch.org/
- **IRC Channel**: #freeswitch on irc.freenode.net
- **GitHub Repository**: https://github.com/signalwire/freeswitch

### Commercial Support
- **SignalWire**: https://signalwire.com/
- **Professional Services**: Available through SignalWire partners

---

**Document Version**: 1.0
**Last Updated**: June 2025
**Tested On**: Debian 12 (bookworm) with FreeSWITCH 1.10.12
